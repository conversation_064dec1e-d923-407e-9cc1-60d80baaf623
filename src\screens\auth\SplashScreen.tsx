// // src/screens/auth/SplashScreen.tsx
// import React, {useEffect, useRef} from 'react';
// import {
//   View,
//   StyleSheet,
//   Animated,
//   Image,
//   Text,
//   Dimensions,
//   StatusBar,
//   Platform,
//   ImageBackground,
// } from 'react-native';
// import {useNavigation} from '@react-navigation/native';
// import {LinearGradient} from 'react-native-linear-gradient';
// import {SafeAreaView} from 'react-native-safe-area-context';
// import SplashScreenLib from 'react-native-splash-screen';

// // Get screen dimensions
// const {width, height} = Dimensions.get('window');

// const SplashScreen = () => {
//   // Navigation
//   const navigation = useNavigation();

//   // Animation values
//   const fadeAnim = useRef(new Animated.Value(0)).current;
//   const fadeAnimFrame2 = useRef(new Animated.Value(0)).current;
//   const logoScale = useRef(new Animated.Value(0.8)).current;
//   const imageRotate1 = useRef(new Animated.Value(0)).current;
//   const imageRotate2 = useRef(new Animated.Value(0)).current;
//   const imageRotate3 = useRef(new Animated.Value(0)).current;

//   // Convert degrees to radians for rotation interpolation
//   const rotateInterpolate1 = imageRotate1.interpolate({
//     inputRange: [0, 1],
//     outputRange: ['0deg', '132.71deg'],
//   });

//   const rotateInterpolate2 = imageRotate2.interpolate({
//     inputRange: [0, 1],
//     outputRange: ['0deg', '-80.55deg'],
//   });

//   const rotateInterpolate3 = imageRotate3.interpolate({
//     inputRange: [0, 1],
//     outputRange: ['0deg', '0.71deg'],
//   });

//   useEffect(() => {
//     // Hide the native splash screen if using react-native-splash-screen
//     if (Platform.OS !== 'web') {
//       SplashScreenLib.hide();
//     }

//     // First frame animation sequence
//     Animated.sequence([
//       // Fade in first frame
//       Animated.timing(fadeAnim, {
//         toValue: 1,
//         duration: 1000,
//         useNativeDriver: true,
//       }),

//       // Wait for GIF to play
//       Animated.delay(2000),

//       // Fade out first frame
//       Animated.timing(fadeAnim, {
//         toValue: 0,
//         duration: 500,
//         useNativeDriver: true,
//       }),
//     ]).start(() => {
//       // Second frame animation sequence
//       Animated.parallel([
//         // Fade in second frame
//         Animated.timing(fadeAnimFrame2, {
//           toValue: 1,
//           duration: 1000,
//           useNativeDriver: true,
//         }),

//         // Scale up logo
//         Animated.timing(logoScale, {
//           toValue: 1,
//           duration: 1200,
//           useNativeDriver: true,
//         }),

//         // Rotate decorative images
//         Animated.timing(imageRotate1, {
//           toValue: 1,
//           duration: 1200,
//           useNativeDriver: true,
//         }),

//         Animated.timing(imageRotate2, {
//           toValue: 1,
//           duration: 1200,
//           useNativeDriver: true,
//         }),

//         Animated.timing(imageRotate3, {
//           toValue: 1,
//           duration: 1200,
//           useNativeDriver: true,
//         }),
//       ]).start(() => {
//         // After all animations, navigate to onboarding/login
//         setTimeout(() => {
//           navigation.navigate('OnboardingScreen' as never);
//         }, 1500);
//       });
//     });

//     // Cleanup function to prevent memory leaks
//     return () => {
//       fadeAnim.stopAnimation();
//       fadeAnimFrame2.stopAnimation();
//       logoScale.stopAnimation();
//       imageRotate1.stopAnimation();
//       imageRotate2.stopAnimation();
//       imageRotate3.stopAnimation();
//     };
//   }, []);

//   return (
//     <SafeAreaView style={styles.container}>
//       <StatusBar translucent backgroundColor="transparent" />
//       <LinearGradient
//         colors={['#EDF7F4', '#E5EFFB']}
//         start={{x: 0, y: 0}}
//         end={{x: 1, y: 0}}
//         style={styles.gradient}>
//         {/* Frame 1 */}
//         <Animated.View style={[styles.frame, {opacity: fadeAnim}]}>
//           <Image
//             source={require('../../assets/images/com-optimize-unscreen.png')}
//             style={styles.gifImage}
//             resizeMode="contain"
//           />
//         </Animated.View>

//         {/* Frame 2 */}
//         <Animated.View style={[styles.frame, {opacity: fadeAnimFrame2}]}>
//           {/* Decorative Image 2 (Top) */}
//           <Animated.Image
//             source={require('../../assets/images/image-removebg-preview.png')}
//             style={[
//               styles.decorativeImage2,
//               {transform: [{rotate: rotateInterpolate1}, {scale: logoScale}]},
//             ]}
//             resizeMode="contain"
//           />

//           {/* Decorative Image 3 (Right) */}
//           <Animated.Image
//             source={require('../../assets/images/image-removebg-preview.png')}
//             style={[
//               styles.decorativeImage3,
//               {transform: [{rotate: rotateInterpolate2}, {scale: logoScale}]},
//             ]}
//             resizeMode="contain"
//           />

//           {/* Logo and Tagline */}
//           <View style={styles.logoContainer}>
//             <Animated.Image
//               source={require('../../assets/images/logo.png')}
//               style={[styles.logoImage, {transform: [{scale: logoScale}]}]}
//               resizeMode="contain"
//             />

//             <Animated.Text
//               style={[styles.logoText, {transform: [{scale: logoScale}]}]}>
//               PAYZLE
//             </Animated.Text>

//             <Animated.Text
//               style={[styles.taglineText, {transform: [{scale: logoScale}]}]}>
//               Your tagline goes here test
//             </Animated.Text>
//           </View>

//           {/* Decorative Image 4 (Bottom) */}
//           <Animated.Image
//             source={require('../../assets/images/image-removebg-preview.png')}
//             style={[
//               styles.decorativeImage4,
//               {transform: [{rotate: rotateInterpolate3}, {scale: logoScale}]},
//             ]}
//             resizeMode="contain"
//           />
//         </Animated.View>
//       </LinearGradient>
//     </SafeAreaView>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
//   gradient: {
//     flex: 1,
//     width: '100%',
//     height: '100%',
//   },
//   frame: {
//     position: 'absolute',
//     width: '100%',
//     height: '100%',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   gifImage: {
//     width: 341,
//     height: 218,
//     position: 'absolute',
//     top: height / 2 - 109, // Center vertically
//     left: width / 2 - 170.5, // Center horizontally
//   },
//   logoContainer: {
//     alignItems: 'center',
//     position: 'absolute',
//     top: height / 2 - 100, // Adjust as needed based on your design
//   },
//   logoImage: {
//     width: 84,
//     height: 161,
//     marginBottom: 10,
//   },
//   logoText: {
//     fontFamily: 'Montserrat-SemiBold',
//     fontWeight: '600',
//     fontSize: 40,
//     lineHeight: 49,
//     textAlign: 'center',
//     color: '#003272',
//   },
//   taglineText: {
//     fontFamily: 'Montserrat-Medium',
//     fontWeight: '500',
//     fontSize: 20,
//     lineHeight: 24,
//     textAlign: 'center',
//     color: '#003272',
//     marginTop: 16,
//   },
//   decorativeImage2: {
//     width: 212.26,
//     height: 124.47,
//     position: 'absolute',
//     top: 15.43,
//     left: width / 2 - 20, // Adjust as needed
//   },
//   decorativeImage3: {
//     width: 178.59,
//     height: 102.06,
//     position: 'absolute',
//     top: 107.17,
//     left: width - 65.59, // Adjust for screen width
//   },
//   decorativeImage4: {
//     width: 393.22,
//     height: 245.24,
//     position: 'absolute',
//     bottom: 0,
//     left: 0.05,
//   },
// });

// export default SplashScreen;

import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';

const SplashScreen = () => {
  return (
    <View style={styles.container}>
      {/* You can add your logo or app name here */}
      {/* <Image
        source={require('../../../assets/logo.png')}
        style={styles.logo}
        resizeMode="contain"
      /> */}
      <Text style={styles.appName}>Your App Name</Text>
      {/* You can add a loading indicator here if you want */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF', // or your preferred background color
  },
  logo: {
    width: 150,
    height: 150,
    marginBottom: 20,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4F46E5', // Indigo color to match your tab bar active color
  },
});

export default SplashScreen;
