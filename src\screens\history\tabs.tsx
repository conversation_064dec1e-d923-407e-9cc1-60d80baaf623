import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import LendHistoryScreen from './lendHistory';
import LoanHistoryScreen from './loanHistory';

type TabType = 'Lend' | 'Loan';

export default function CustomTabs() {
  const [activeTab, setActiveTab] = useState<TabType>('Lend');

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'Lend' && styles.activeTabButton,
          ]}
          onPress={() => handleTabPress('Lend')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'Lend' && styles.activeTabText,
            ]}>
            Lend History
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'Loan' && styles.activeTabButton,
          ]}
          onPress={() => handleTabPress('Loan')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'Loan' && styles.activeTabText,
            ]}>
            Loan History
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {activeTab === 'Lend' ? <LendHistoryScreen /> : <LoanHistoryScreen />}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    padding: 4,
    margin: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTabButton: {
    backgroundColor: '#4F46E5',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
});
