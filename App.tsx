import React, {useEffect, useState} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import SplashScreen from 'react-native-splash-screen';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import TabNavigator from './src/navigation/tabNavigator';
import NotificationsScreen from './src/screens/notifications';
import LoginScreen from './src/screens/auth/login';
import PhoneNumberScreen from './src/screens/auth/login/phoneNumberScreen';
import OTPVerificationScreen from './src/screens/auth/login/OTPVerificationScreen';
import AccountScreen from './src/screens/account';
import AccountProfileScreen from './src/screens/account/accountProfile';
import HistoryScreen from './src/screens/account/history';
import FriendsScreen from './src/screens/account/friends';
import ActiveCasesScreen from './src/screens/account/activeCases';
import TagsScreen from './src/screens/account/tags';
import SettingsScreen from './src/screens/account/settings';
import BorrowMoneyScreen from './src/screens/borrowMoney';
import RepayMoneyScreen from './src/screens/repayMoney';
import LoanHistoryDetails from './src/screens/history/loanHistory/LoanHistoryDetails';
import {AuthProvider} from './src/contexts/AuthContext';
import TestOTPScreen from './src/screens/TestOTPScreen';
import {configureAmplify} from './src/config/aws-config';
import {SafeAreaView, StyleSheet} from 'react-native';
import './global.css';

// Define the navigation types
export type RootStackParamList = {
  Main: undefined; // No parameters for this screen
  BorrowMoney: undefined; // No parameters for this screen
  RepayMoney: undefined; // No parameters for this screen
  Notifications: undefined; // No parameters for this screen
  Account: undefined; // No parameters for this screen
  Profile: undefined; // No parameters for this screen
  History: undefined; // No parameters for this screen
  Friends: undefined; // No parameters for this screen
  ActiveCases: undefined; // No parameters for this screen
  Tags: undefined; // No parameters for this screen
  Settings: undefined; // No parameters for this screen
  Login: undefined; // No parameters for this screen
  PhoneNumber: undefined; // No parameters for this screen
  OTPVerification: {phoneNumber: string}; // Pass phone number as a parameter
  TestOTP: undefined; // No parameters for this screen
  // LoanHistory
  LoanHistoryDetails: {
    friend: any;
  };
};

const Stack = createNativeStackNavigator<RootStackParamList>();

// Initialize Amplify
configureAmplify();

export default function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(true); // 👈 replace with your real auth logic

  useEffect(() => {
    SplashScreen.hide();
    // Here you would typically check async storage or a secure store for auth tokens
    // For now, it assumes not logged in
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <AuthProvider>
        <NavigationContainer>
          <Stack.Navigator screenOptions={{headerShown: false}}>
            {isLoggedIn ? (
              <>
                <Stack.Screen name="Main" component={TabNavigator} />
                <Stack.Screen
                  name="Notifications"
                  component={NotificationsScreen}
                />
                {/* home page */}
                <Stack.Screen
                  name="BorrowMoney"
                  component={BorrowMoneyScreen}
                />
                <Stack.Screen name="RepayMoney" component={RepayMoneyScreen} />

                {/* account routing */}
                <Stack.Screen name="Account" component={AccountScreen} />
                <Stack.Screen name="Profile" component={AccountProfileScreen} />
                <Stack.Screen name="History" component={HistoryScreen} />
                <Stack.Screen name="Friends" component={FriendsScreen} />
                <Stack.Screen
                  name="ActiveCases"
                  component={ActiveCasesScreen}
                />
                <Stack.Screen name="Tags" component={TagsScreen} />
                <Stack.Screen name="Settings" component={SettingsScreen} />

                {/* history */}
                <Stack.Screen
                  name="LoanHistoryDetails"
                  component={LoanHistoryDetails}
                />
              </>
            ) : (
              <>
                <Stack.Screen name="Login" component={LoginScreen} />
                <Stack.Screen
                  name="PhoneNumber"
                  component={PhoneNumberScreen}
                />
                <Stack.Screen
                  name="OTPVerification"
                  component={OTPVerificationScreen}
                  initialParams={{phoneNumber: ''}} // Pass phone number as an initial param
                />
                <Stack.Screen name="TestOTP" component={TestOTPScreen} />
              </>
            )}
          </Stack.Navigator>
        </NavigationContainer>
      </AuthProvider>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
