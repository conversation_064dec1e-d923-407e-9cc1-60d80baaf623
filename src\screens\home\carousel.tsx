import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Animated,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const CARD_WIDTH = 361; // Fixed width as per design
const CARD_HEIGHT = 190; // Fixed height as per design
const SPACING = 12;
const SIDE_SPACING = (width - CARD_WIDTH) / 2;

const data = [
  {
    id: '1',
    title: 'Repayment day',
    text: 'Gentle Reminder: You have to Repay to Saksham Rs. 15,000 today.',
  },
  {
    id: '2',
    title: 'Upcoming Repayment',
    text: 'Heads-up: Repayment to Rohit of Rs. 10,000 is due tomorrow.',
  },
  {
    id: '3',
    title: 'Payment Due Soon',
    text: 'Reminder: You owe Priya Rs. 5,000 in 3 days.',
  },
];

// Add duplicates at start and end for smooth infinite scroll
const loopedData = [...data, ...data, ...data];

const RepaymentCarousel = () => {
  const navigation = useNavigation();
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(data.length);
  const scrollX = useRef(new Animated.Value(0)).current;
  const [isScrolling, setIsScrolling] = useState(false);

  // Function to handle automatic scrolling
  const scrollToIndex = (index: number, animated = true) => {
    flatListRef.current?.scrollToIndex({
      index,
      animated,
    });
  };

  // Reset to middle set when reaching the ends
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffset / (CARD_WIDTH + SPACING));

    // If we're at the start of the last set
    if (currentIndex >= data.length * 2) {
      // Jump to middle set without animation
      scrollToIndex(currentIndex - data.length, false);
    }
    // If we're at the end of the first set
    else if (currentIndex <= data.length - 1) {
      // Jump to middle set without animation
      scrollToIndex(currentIndex + data.length, false);
    }
  };

  // Auto scroll effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    const startAutoScroll = () => {
      interval = setInterval(() => {
        if (!isScrolling) {
          const nextIndex = (currentIndex % data.length) + 1 + data.length;
          scrollToIndex(nextIndex);
        }
      }, 3000);
    };

    startAutoScroll();

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [currentIndex, isScrolling]);

  const onMomentumScrollEnd = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffset / (CARD_WIDTH + SPACING));
    setCurrentIndex(newIndex % data.length);
    setIsScrolling(false);
  };

  const handleLater = () => {
    // Handle later action
  };

  const handleViewMore = () => {
    navigation.navigate('RepayMoney' as never);
  };

  return (
    <View style={styles.container}>
      <Animated.FlatList
        ref={flatListRef}
        data={loopedData}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: SIDE_SPACING,
        }}
        snapToInterval={CARD_WIDTH + SPACING}
        decelerationRate="fast"
        onScroll={Animated.event(
          [{nativeEvent: {contentOffset: {x: scrollX}}}],
          {
            useNativeDriver: true,
            listener: handleScroll,
          },
        )}
        scrollEventThrottle={16}
        onMomentumScrollEnd={onMomentumScrollEnd}
        onScrollBeginDrag={() => setIsScrolling(true)}
        initialScrollIndex={data.length}
        getItemLayout={(_, index) => ({
          length: CARD_WIDTH + SPACING,
          offset: (CARD_WIDTH + SPACING) * index,
          index,
        })}
        renderItem={({item, index}) => {
          const inputRange = [
            (index - 2) * (CARD_WIDTH + SPACING),
            (index - 1) * (CARD_WIDTH + SPACING),
            index * (CARD_WIDTH + SPACING),
          ];

          const scale = scrollX.interpolate({
            inputRange,
            outputRange: [0.95, 1, 0.95],
            extrapolate: 'clamp',
          });

          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.6, 1, 0.6],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              style={[
                styles.cardContainer,
                {
                  width: CARD_WIDTH,
                  height: CARD_HEIGHT,
                  marginHorizontal: SPACING / 2,
                  transform: [{scale}],
                  opacity,
                },
              ]}>
              <View style={styles.card}>
                {/* Main content */}
                <View style={styles.cardContent}>
                  <Text style={styles.title}>{item.title}</Text>
                  <Text style={styles.description}>{item.text}</Text>
                </View>

                {/* Action buttons */}
                <View style={styles.buttonContainer}>
                  <TouchableOpacity onPress={handleLater}>
                    <Text style={styles.laterButtonText}>Later</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={handleViewMore}>
                    <Text style={styles.viewMoreButtonText}>View more</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          );
        }}
      />

      <View style={styles.indicatorContainer}>
        {data.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              currentIndex % data.length === index && styles.activeDot,
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: CARD_HEIGHT + 40, // Extra space for indicators
  },
  cardContainer: {
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    overflow: 'hidden',
  },
  card: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 20,
  },
  cardContent: {
    flex: 1,
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  laterButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '400',
  },
  viewMoreButtonText: {
    fontSize: 16,
    color: '#0066FF',
    fontWeight: '500',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 102, 255, 0.3)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#0066FF',
    width: 20,
    borderRadius: 4,
  },
});

export default RepaymentCarousel;
