import React from 'react';
import {View, Text, Image, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

const LoanHistoryDetails = ({route}: any) => {
  const {friend} = route.params;

  const interest = 0.05;
  const numericAmount = parseInt(friend.amount.replace(/[^0-9]/g, ''), 10);
  const interestAmount = numericAmount * interest;
  const total = numericAmount + interestAmount;

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Lend History</Text>

      <View style={styles.userSection}>
        <Image source={{uri: friend.profileImage}} style={styles.avatar} />
        <View>
          <Text style={styles.name}>{friend.name}</Text>
          <Text style={styles.tag}>{friend.tag}</Text>
        </View>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.label}>Repayment date</Text>
        <Text>{friend.dueDate}</Text>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.label}>Loan Duration</Text>
        <Text>4 months</Text>
      </View>

      <View style={styles.repayBox}>
        <View style={styles.repayRow}>
          <Text>Amount</Text>
          <Text>₹{numericAmount}</Text>
        </View>
        <View style={styles.repayRow}>
          <Text>Interest (5%)</Text>
          <Text>₹{interestAmount}</Text>
        </View>
        <View style={[styles.repayRow, styles.totalRow]}>
          <Text style={styles.totalText}>Total</Text>
          <Text style={styles.totalText}>₹{total}</Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, padding: 16},
  title: {fontSize: 20, fontWeight: 'bold', marginBottom: 20},
  userSection: {flexDirection: 'row', alignItems: 'center', marginBottom: 20},
  avatar: {width: 50, height: 50, borderRadius: 25, marginRight: 12},
  name: {fontSize: 18, fontWeight: '600'},
  tag: {fontSize: 14, color: '#4CAF50'},
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  label: {fontWeight: 'bold'},
  repayBox: {
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#f1f1f1',
  },
  repayRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  totalRow: {
    borderTopWidth: 1,
    borderColor: '#ccc',
    paddingTop: 8,
    marginTop: 8,
  },
  totalText: {fontWeight: 'bold'},
});

export default LoanHistoryDetails;
