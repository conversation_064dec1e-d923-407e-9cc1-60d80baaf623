import React, {useState} from 'react';
import {View, Text, ScrollView, TouchableOpacity, StyleSheet} from 'react-native';
import Layout from '../../components/Layout';
import {
  Bell,
  UserPlus,
  CurrencyCircleDollar,
  CheckCircle,
  Clock,
  CaretRight,
  Check,
} from 'phosphor-react-native';

interface Notification {
  id: string;
  type: 'friend_request' | 'loan_request' | 'payment' | 'reminder';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionable?: boolean;
}

// Group notifications by date
interface GroupedNotifications {
  [key: string]: Notification[];
}

const notifications: Notification[] = [
  {
    id: '1',
    type: 'friend_request',
    title: 'New Friend Request',
    message: '<PERSON><PERSON> wants to connect with you',
    timestamp: '2024-03-20T10:30:00',
    read: false,
    actionable: true,
  },
  {
    id: '2',
    type: 'loan_request',
    title: 'Loan Request',
    message: '<PERSON><PERSON> requested ₹5,000',
    timestamp: '2024-03-20T09:15:00',
    read: false,
    actionable: true,
  },
  {
    id: '3',
    type: 'payment',
    title: 'Payment Received',
    message: '<PERSON><PERSON><PERSON> paid ₹2,000',
    timestamp: '2024-03-19T15:45:00',
    read: true,
  },
  {
    id: '4',
    type: 'reminder',
    title: 'Payment Reminder',
    message: 'Payment of ₹3,000 due tomorrow',
    timestamp: '2024-03-19T11:20:00',
    read: true,
  },
  {
    id: '5',
    type: 'payment',
    title: 'Payment Successful',
    message: 'You paid ₹1,500 to Priya Sharma',
    timestamp: '2024-03-18T14:30:00',
    read: true,
  },
];

export default function NotificationsScreen() {
  const [notificationsList, setNotificationsList] = useState(notifications);

  // Group notifications by date
  const groupedNotifications = notificationsList.reduce((groups: GroupedNotifications, notification) => {
    const date = new Date(notification.timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    let dateString: string;
    if (date.toDateString() === today.toDateString()) {
      dateString = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      dateString = 'Yesterday';
    } else {
      dateString = date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
      });
    }

    if (!groups[dateString]) {
      groups[dateString] = [];
    }
    groups[dateString].push(notification);
    return groups;
  }, {});

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'friend_request':
        return <UserPlus size={24} color="#4F46E5" weight="duotone" />;
      case 'loan_request':
        return <CurrencyCircleDollar size={24} color="#4F46E5" weight="duotone" />;
      case 'payment':
        return <CheckCircle size={24} color="#059669" weight="duotone" />;
      case 'reminder':
        return <Clock size={24} color="#DC2626" weight="duotone" />;
      default:
        return <Bell size={24} color="#4F46E5" weight="duotone" />;
    }
  };

  const handleNotificationPress = (notification: Notification) => {
    if (!notification.read) {
      // Mark as read
      setNotificationsList(prev =>
        prev.map(n =>
          n.id === notification.id ? {...n, read: true} : n,
        ),
      );
    }
    // Handle navigation or action based on notification type
    console.log('Notification pressed:', notification.type);
  };

  const handleMarkAllAsRead = () => {
    setNotificationsList(prev =>
      prev.map(n => ({...n, read: true})),
    );
  };

  const unreadCount = notificationsList.filter(n => !n.read).length;

  const renderRightContent = () => {
    if (unreadCount > 0) {
      return (
        <TouchableOpacity
          style={styles.markAllReadButton}
          onPress={handleMarkAllAsRead}>
          <Text style={styles.markAllReadText}>Mark all as read</Text>
        </TouchableOpacity>
      );
    }
    return null;
  };

  return (
    <Layout
      title="Notifications"
      showBack
      style={styles.container}
      rightContent={renderRightContent()}>
      {/* Header with unread count */}
      {unreadCount > 0 && (
        <View style={styles.header}>
          <View style={styles.unreadCount}>
            <Text style={styles.unreadCountText}>
              {unreadCount} unread
            </Text>
          </View>
        </View>
      )}

      {notificationsList.length > 0 ? (
        <ScrollView style={styles.scrollView}>
          {Object.entries(groupedNotifications).map(([date, items]) => (
            <View key={date} style={styles.section}>
              <Text style={styles.dateHeader}>{date}</Text>
              {items.map(notification => (
                <TouchableOpacity
                  key={notification.id}
                  style={[
                    styles.notificationCard,
                    !notification.read && styles.unreadCard,
                  ]}
                  onPress={() => handleNotificationPress(notification)}>
                  <View style={[
                    styles.notificationIcon,
                    !notification.read && styles.unreadIcon,
                  ]}>
                    {getNotificationIcon(notification.type)}
                  </View>
                  <View style={styles.notificationContent}>
                    <Text style={[
                      styles.notificationTitle,
                      !notification.read && styles.unreadText,
                    ]}>
                      {notification.title}
                    </Text>
                    <Text style={styles.notificationMessage}>
                      {notification.message}
                    </Text>
                    <Text style={styles.timestamp}>
                      {new Date(notification.timestamp).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true,
                      })}
                    </Text>
                  </View>
                  {notification.actionable && (
                    <CaretRight size={20} color="#9CA3AF" weight="bold" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyState}>
          <Bell size={48} color="#9CA3AF" weight="duotone" />
          <Text style={styles.emptyStateTitle}>No notifications</Text>
          <Text style={styles.emptyStateMessage}>
            You're all caught up! Check back later for new notifications.
          </Text>
        </View>
      )}
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  unreadCount: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  unreadCountText: {
    color: '#4F46E5',
    fontSize: 14,
    fontWeight: '600',
  },
  markAllReadButton: {
    padding: 8,
  },
  markAllReadText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  dateHeader: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F3F4F6',
  },
  notificationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  unreadCard: {
    backgroundColor: '#EEF2FF',
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadIcon: {
    backgroundColor: '#EEF2FF',
    borderWidth: 2,
    borderColor: '#4F46E5',
  },
  notificationContent: {
    flex: 1,
    marginRight: 8,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  unreadText: {
    color: '#4F46E5',
  },
  notificationMessage: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
}); 