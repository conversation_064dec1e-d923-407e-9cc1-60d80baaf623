import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {ArrowLeft} from 'phosphor-react-native';

type CustomHeaderProps = {
  title: string;
  backTo?: string;
};

export default function TitleHeader({title, backTo}: CustomHeaderProps) {
  const navigation = useNavigation();

  const handleBack = () => {
    if (backTo) {
      navigation.navigate(backTo as never);
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.container}>
      {backTo && (
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#000" />
        </TouchableOpacity>
      )}
      <Text style={styles.title}>{title}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
});
