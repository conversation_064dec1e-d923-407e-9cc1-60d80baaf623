import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  TextInput,
} from 'react-native';
import Layout from '../../components/Layout';
import {
  CaretDown,
  DotsThreeVertical,
  MagnifyingGlass,
  Plus,
  UserPlus,
} from 'phosphor-react-native';

interface Friend {
  id: string;
  name: string;
  tag: string;
  profile: string;
  tagColor: string;
  limit?: string;
  button?: string;
}

const initialFriends: Friend[] = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    tag: 'Close Friends',
    profile: 'https://i.pravatar.cc/100?img=1',
    tagColor: '#DFF5E1',
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    tag: 'Close Friends',
    profile: 'https://i.pravatar.cc/100?img=2',
    tagColor: '#DFF5E1',
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    tag: 'College Friends',
    limit: '₹1,00,000',
    profile: 'https://i.pravatar.cc/100?img=3',
    tagColor: '#E3EEF9',
    button: 'Borrow',
  },
  // Add more friends as needed
];

export default function FriendsScreen() {
  const [friends] = useState<Friend[]>(initialFriends);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter friends based on search query
  const filteredFriends = friends.filter(friend => {
    const query = searchQuery.toLowerCase().trim();
    return (
      friend.name.toLowerCase().includes(query) ||
      friend.tag.toLowerCase().includes(query)
    );
  });

  // Group filtered friends by first letter
  const groupedFriends = filteredFriends.reduce((acc, friend) => {
    const firstLetter = friend.name[0].toUpperCase();
    if (!acc[firstLetter]) {
      acc[firstLetter] = [];
    }
    acc[firstLetter].push(friend);
    return acc;
  }, {} as Record<string, Friend[]>);

  // Sort the groups alphabetically
  const sortedGroups = Object.entries(groupedFriends).sort(([a], [b]) => 
    a.localeCompare(b)
  );

  return (
    <Layout
      title="Friends"
      showNotification
      showProfile
      style={styles.layoutContainer}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <MagnifyingGlass size={20} color="#6B7280" weight="bold" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search friends"
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
            autoCapitalize="none"
            autoCorrect={false}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => setSearchQuery('')}
              style={styles.clearButton}>
              <Text style={styles.clearButtonText}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.scrollView}>
        {sortedGroups.length > 0 ? (
          sortedGroups.map(([letter, letterFriends]) => (
            <View key={letter} style={styles.section}>
              <Text style={styles.sectionLabel}>{letter}</Text>
              {letterFriends.map(friend => (
                <View key={friend.id} style={styles.card}>
                  <Image source={{uri: friend.profile}} style={styles.avatar} />
                  <View style={styles.friendInfo}>
                    <Text style={styles.name}>{friend.name}</Text>
                    <View
                      style={[styles.tagBox, {backgroundColor: friend.tagColor}]}>
                      <Text style={styles.tagText}>{friend.tag}</Text>
                      {friend.limit && (
                        <Text style={styles.limitText}>Limit: {friend.limit}</Text>
                      )}
                    </View>
                  </View>

                  {friend.button ? (
                    <TouchableOpacity style={styles.borrowButton}>
                      <Text style={styles.borrowText}>Borrow</Text>
                    </TouchableOpacity>
                  ) : (
                    <View style={styles.actionButtons}>
                      <TouchableOpacity style={styles.iconButton}>
                        <CaretDown size={20} color="#6B7280" weight="bold" />
                      </TouchableOpacity>
                      <TouchableOpacity style={styles.iconButton}>
                        <DotsThreeVertical
                          size={20}
                          color="#6B7280"
                          weight="bold"
                        />
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              ))}
            </View>
          ))
        ) : (
          <View style={styles.noResults}>
            <Text style={styles.noResultsText}>No friends found.</Text>
          </View>
        )}

        {/* Suggested Friends */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Suggested Friends</Text>
          {[1, 2].map(index => (
            <View key={index} style={styles.suggestedCard}>
              <View style={styles.suggestedAvatar}>
                <UserPlus size={24} color="#4F46E5" weight="bold" />
              </View>
              <Text style={styles.suggestedName}>Bineeta Roy</Text>
              <TouchableOpacity style={styles.inviteButton}>
                <Text style={styles.inviteText}>Invite</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Add Friends Button */}
      <TouchableOpacity style={styles.addFriendButton}>
        <Plus size={20} color="#FFFFFF" weight="bold" />
        <Text style={styles.addFriendText}>Add Friends</Text>
      </TouchableOpacity>
    </Layout>
  );
}

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#F9FAFB',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#1F2937',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
    paddingHorizontal: 16,
    marginBottom: 8,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  friendInfo: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  tagBox: {
    borderRadius: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
    alignSelf: 'flex-start',
  },
  tagText: {
    fontSize: 12,
    color: '#1F2937',
    fontWeight: '500',
  },
  limitText: {
    fontSize: 11,
    color: '#4B5563',
    marginTop: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: 8,
  },
  borrowButton: {
    backgroundColor: '#4F46E5',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  borrowText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  suggestedCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  suggestedAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  suggestedName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
  },
  inviteButton: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  inviteText: {
    color: '#4F46E5',
    fontWeight: '600',
    fontSize: 14,
  },
  addFriendButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    backgroundColor: '#4F46E5',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  addFriendText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  clearButton: {
    padding: 8,
  },
  clearButtonText: {
    color: '#6B7280',
    fontSize: 16,
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noResultsText: {
    color: '#6B7280',
    fontSize: 16,
  },
});
