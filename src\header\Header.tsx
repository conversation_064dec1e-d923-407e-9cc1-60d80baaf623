import React, {ReactNode} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {
  ArrowLeft,
  Bell,
  User,
  DotsThreeVertical,
} from 'phosphor-react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors, shadows} from '../theme/colors';

type HeaderProps = {
  title: string;
  showBack?: boolean;
  showNotification?: boolean;
  showProfile?: boolean;
  showMenu?: boolean;
  onNotificationPress?: () => void;
  onProfilePress?: () => void;
  onMenuPress?: () => void;
  transparent?: boolean;
  rightContent?: ReactNode;
};

export default function Header({
  title,
  showBack = false,
  showNotification = false,
  showProfile = false,
  showMenu = false,
  onNotificationPress,
  onProfilePress,
  onMenuPress,
  transparent = false,
  rightContent,
}: HeaderProps) {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <>
      <StatusBar
        barStyle={transparent ? 'dark-content' : 'light-content'}
        backgroundColor={transparent ? colors.common.white : colors.primary.main}
        translucent
      />
      {transparent ? (
        <View
          style={[
            styles.container,
            styles.transparentBg,
            {paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight! + 10},
          ]}>
          {/* Header Content */}
          <View style={styles.leftSection}>
            {showBack && (
              <TouchableOpacity onPress={handleBack} style={styles.iconButton}>
                <ArrowLeft size={24} color={colors.primary.main} weight="bold" />
              </TouchableOpacity>
            )}
            <Text style={[styles.title, styles.darkText]}>{title}</Text>
          </View>

          <View style={styles.rightSection}>
            {rightContent ? (
              rightContent
            ) : (
              <>
                {showNotification && (
                  <TouchableOpacity
                    onPress={onNotificationPress}
                    style={styles.iconButton}>
                    <Bell size={24} color={colors.primary.main} weight="bold" />
                  </TouchableOpacity>
                )}

                {showProfile && (
                  <TouchableOpacity
                    onPress={onProfilePress}
                    style={styles.iconButton}>
                    <User size={24} color={colors.primary.main} weight="bold" />
                  </TouchableOpacity>
                )}

                {showMenu && (
                  <TouchableOpacity onPress={onMenuPress} style={styles.iconButton}>
                    <DotsThreeVertical size={24} color={colors.primary.main} weight="bold" />
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
        </View>
      ) : (
        <LinearGradient
          colors={[colors.primary.main, colors.primary.light]}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}
          style={[
            styles.container,
            {paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight! + 10},
          ]}>
          {/* Header Content */}
          <View style={styles.leftSection}>
            {showBack && (
              <TouchableOpacity onPress={handleBack} style={styles.iconButton}>
                <ArrowLeft size={24} color={colors.common.white} weight="bold" />
              </TouchableOpacity>
            )}
            <Text style={[styles.title, styles.lightText]}>{title}</Text>
          </View>

          <View style={styles.rightSection}>
            {rightContent ? (
              rightContent
            ) : (
              <>
                {showNotification && (
                  <TouchableOpacity
                    onPress={onNotificationPress}
                    style={styles.iconButton}>
                    <Bell size={24} color={colors.common.white} weight="bold" />
                  </TouchableOpacity>
                )}

                {showProfile && (
                  <TouchableOpacity
                    onPress={onProfilePress}
                    style={styles.iconButton}>
                    <User size={24} color={colors.common.white} weight="bold" />
                  </TouchableOpacity>
                )}

                {showMenu && (
                  <TouchableOpacity onPress={onMenuPress} style={styles.iconButton}>
                    <DotsThreeVertical size={24} color={colors.common.white} weight="bold" />
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
        </LinearGradient>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16,
    ...shadows.md,
  },
  transparentBg: {
    backgroundColor: colors.common.white,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 8,
  },
  lightText: {
    color: colors.common.white,
  },
  darkText: {
    color: colors.text.primary,
  },
  iconButton: {
    padding: 8,
    marginLeft: 8,
  },
}); 