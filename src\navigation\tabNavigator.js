import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {
  House,
  FileText,
  ClockCounterClockwise,
  Users,
} from 'phosphor-react-native';

import HomeScreen from '../screens/home';
import FriendsScreen from '../screens/friends';
import HistoryScreen from '../screens/history';
import RequestsScreen from '../screens/requests';

const Tab = createBottomTabNavigator();

export default function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({color, size}) => {
          const iconProps = {color, size};

          switch (route.name) {
            case 'Home':
              return <House {...iconProps} />;
            case 'Request':
              return <FileText {...iconProps} />;
            case 'History':
              return <ClockCounterClockwise {...iconProps} />;
            case 'Friend':
              return <Users {...iconProps} />;
            default:
              return null;
          }
        },
        tabBarActiveTintColor: '#6200ee',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}>
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Friend" component={FriendsScreen} />
      <Tab.Screen name="Request" component={RequestsScreen} />
      <Tab.Screen name="History" component={HistoryScreen} />
    </Tab.Navigator>
  );
}
