import React from 'react';
import {Text, StyleSheet, ScrollView, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import TitleHeader from '../../../header/titleHeader';
import LoanCard from '../../../globalComponents/LoanCard';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

type RootStackParamList = {
  LoanHistoryDetails: {friend: any};
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const BorrowerDataArray = [
  {
    Date: '24th Mar, 2024',
    lendData: [
      {
        name: '<PERSON>',
        tag: 'Roommate',
        amount: '₹25,000',
        dueDate: '24th Apr, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=5',
        onRepay: () => console.log('Repay pressed for <PERSON>'),
        gradientColors: ['#E0F7FA', '#B2EBF2'],
      },
      {
        name: '<PERSON>',
        tag: 'Neighbor',
        amount: '₹8,000',
        dueDate: '30th Apr, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=6',
        onRepay: () => console.log('Repay pressed for Alex Thompson'),
        gradientColors: ['#FCE4EC', '#F8BBD0'],
      }
    ],
  },
  {
    Date: '20th Mar, 2024',
    lendData: [
      {
        name: 'Raj Malhotra',
        tag: 'Business Associate',
        amount: '₹40,000',
        dueDate: '20th May, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=7',
        onRepay: () => console.log('Repay pressed for Raj Malhotra'),
        gradientColors: ['#FFF3E0', '#FFE0B2'],
      },
      {
        name: 'Lisa Wang',
        tag: 'Project Partner',
        amount: '₹15,000',
        dueDate: '20th Jun, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=8',
        onRepay: () => console.log('Repay pressed for Lisa Wang'),
        gradientColors: ['#E8F5E9', '#C8E6C9'],
      }
    ],
  }
];

const BorrowerModeScreen = () => {
  const navigation = useNavigation<NavigationProp>();

  return (
    <View style={styles.container}>
      <ScrollView>
        {BorrowerDataArray.map((item, index) => (
          <View style={styles.container} key={index}>
            <Text style={styles.date}>{item.Date}</Text>
            {item.lendData.map((friend, friendIndex) => (
              <LoanCard
                key={friendIndex}
                name={friend.name}
                tag={friend.tag}
                amount={friend.amount}
                dueDate={friend.dueDate}
                profileImage={friend.profileImage}
                onRepay={() =>
                  navigation.navigate('LoanHistoryDetails', {friend})
                }
                gradientColors={friend.gradientColors}
              />
            ))}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 4,
  },
  scrollContent: {
    // paddingVertical: 16,
    // paddingHorizontal: 16,
  },
  date: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: 'Montserrat-SemiBold',
  },
});

export default BorrowerModeScreen;
