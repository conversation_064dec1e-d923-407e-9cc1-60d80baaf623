import React from 'react';
import {StyleSheet, ScrollView, View, Text} from 'react-native';
import LoanCard from '../../../globalComponents/LoanCard';
import {useNavigation} from '@react-navigation/native';
const LoanDataArray = [
  {
    Date: '2nd Jan, 2025',
    lendData: [
      {
        name: '<PERSON><PERSON><PERSON>',
        tag: 'Real Friends',
        amount: '₹20,000',
        dueDate: '2nd Jan, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=3',
        onRepay: () => console.log('Repay pressed for <PERSON><PERSON><PERSON>'),
        gradientColors: ['#E0F7FA', '#B2EBF2'],
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        tag: 'College Buddies',
        amount: '₹15,500',
        dueDate: '12th Feb, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=5',
        onRepay: () => console.log('Repay pressed for <PERSON>ira<PERSON>'),
        gradientColors: ['#FCE4EC', '#F8BBD0'],
      },
      {
        name: '<PERSON><PERSON>',
        tag: 'Close Circle',
        amount: '₹10,000',
        dueDate: '5th Mar, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=7',
        onRepay: () => console.log('Repay pressed for KL Rahul'),
        gradientColors: ['#FFF3E0', '#FFE0B2'],
      },
      {
        name: 'Hardik Pandya',
        tag: 'Gym Bro',
        amount: '₹8,200',
        dueDate: '18th Apr, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=9',
        onRepay: () => console.log('Repay pressed for Hardik Pandya'),
        gradientColors: ['#E8F5E9', '#C8E6C9'],
      },
      {
        name: 'MS Dhoni',
        tag: 'Mentor',
        amount: '₹25,000',
        dueDate: '30th May, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=11',
        onRepay: () => console.log('Repay pressed for MS Dhoni'),
        gradientColors: ['#EDE7F6', '#D1C4E9'],
      },
    ],
  },
  {
    Date: '2nd Jan, 2025',
    lendData: [
      {
        name: 'Rohit Sharma',
        tag: 'Real Friends',
        amount: '₹20,000',
        dueDate: '2nd Jan, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=3',
        onRepay: () => console.log('Repay pressed for Rohit Sharma'),
        gradientColors: ['#E0F7FA', '#B2EBF2'],
      },
      {
        name: 'Virat Kohli',
        tag: 'College Buddies',
        amount: '₹15,500',
        dueDate: '12th Feb, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=5',
        onRepay: () => console.log('Repay pressed for Virat Kohli'),
        gradientColors: ['#FCE4EC', '#F8BBD0'],
      },
      {
        name: 'KL Rahul',
        tag: 'Close Circle',
        amount: '₹10,000',
        dueDate: '5th Mar, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=7',
        onRepay: () => console.log('Repay pressed for KL Rahul'),
        gradientColors: ['#FFF3E0', '#FFE0B2'],
      },
      {
        name: 'Hardik Pandya',
        tag: 'Gym Bro',
        amount: '₹8,200',
        dueDate: '18th Apr, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=9',
        onRepay: () => console.log('Repay pressed for Hardik Pandya'),
        gradientColors: ['#E8F5E9', '#C8E6C9'],
      },
      {
        name: 'MS Dhoni',
        tag: 'Mentor',
        amount: '₹25,000',
        dueDate: '30th May, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=11',
        onRepay: () => console.log('Repay pressed for MS Dhoni'),
        gradientColors: ['#EDE7F6', '#D1C4E9'],
      },
    ],
  },
];
const LoanHistoryScreen = () => {
  const navigation = useNavigation(); // Get the navigation prop

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {LoanDataArray.map((item, index) => (
          <View style={styles.container}>
            <Text style={styles.date}>{item.Date}</Text>
            {item.lendData.map((friend, index) => (
              <LoanCard
                key={index}
                name={friend.name}
                tag={friend.tag}
                amount={friend.amount}
                dueDate={friend.dueDate}
                profileImage={friend.profileImage}
                onRepay={() =>
                  navigation.navigate('LoanHistoryDetails' as never, {friend})
                }
                gradientColors={friend.gradientColors}
              />
            ))}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 4,
  },
  scrollContent: {
    // paddingVertical: 16,
    // paddingHorizontal: 16,
  },
  date: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: 'Montserrat-SemiBold',
  },
});

export default LoanHistoryScreen;
