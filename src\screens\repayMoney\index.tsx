import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
} from 'react-native';
import Layout from '../../components/Layout';
import {
  CurrencyDollar,
  User,
  CaretRight,
  MagnifyingGlass,
  Bank,
  CreditCard,
  Wallet,
} from 'phosphor-react-native';

interface Loan {
  id: string;
  lender: string;
  amount: number;
  dueDate: string;
  status: 'Due' | 'Overdue' | 'Paid';
  profileImage: string;
}

const RepayMoneyScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);

  // Mock data for loans to repay
  const loans: Loan[] = [
    {
      id: '1',
      lender: '<PERSON>',
      amount: 35000,
      dueDate: '2024-04-24',
      status: 'Due',
      profileImage: 'https://i.pravatar.cc/150?img=1',
    },
    {
      id: '2',
      lender: '<PERSON>',
      amount: 12500,
      dueDate: '2024-04-30',
      status: 'Overdue',
      profileImage: 'https://i.pravatar.cc/150?img=2',
    },
    {
      id: '3',
      lender: 'Priya Patel',
      amount: 50000,
      dueDate: '2024-05-20',
      status: 'Due',
      profileImage: 'https://i.pravatar.cc/150?img=3',
    },
  ];

  const totalOutstanding = loans
    .filter(loan => loan.status !== 'Paid')
    .reduce((sum, loan) => sum + loan.amount, 0);

  const handlePaymentMethodSelect = (method: string) => {
    setSelectedPaymentMethod(method);
  };

  const handleRepayPress = (loan: Loan) => {
    if (!selectedPaymentMethod) {
      Alert.alert('Select Payment Method', 'Please select a payment method before proceeding.');
      return;
    }
    Alert.alert(
      'Confirm Repayment',
      `Are you sure you want to repay ₹${loan.amount.toLocaleString()} to ${loan.lender}?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Confirm',
          onPress: () => {
            // Handle repayment logic here
            Alert.alert('Success', 'Payment initiated successfully!');
          },
        },
      ],
    );
  };

  const renderLoanItem = ({item}: {item: Loan}) => (
    <TouchableOpacity 
      style={styles.loanItem}
      onPress={() => handleRepayPress(item)}
    >
      <View style={styles.loanItemLeft}>
        <View style={styles.lenderIcon}>
          <User size={24} color="#4F46E5" weight="bold" />
        </View>
        <View style={styles.loanDetails}>
          <Text style={styles.lenderName}>{item.lender}</Text>
          <Text style={styles.dueDate}>
            Due: {new Date(item.dueDate).toLocaleDateString('en-IN', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            })}
          </Text>
        </View>
      </View>
      
      <View style={styles.loanItemRight}>
        <Text style={styles.amount}>₹{item.amount.toLocaleString()}</Text>
        <View
          style={[
            styles.statusBadge,
            item.status === 'Overdue' && styles.overdueBadge,
            item.status === 'Paid' && styles.paidBadge,
          ]}>
          <Text
            style={[
              styles.statusText,
              item.status === 'Overdue' && styles.overdueText,
              item.status === 'Paid' && styles.paidText,
            ]}>
            {item.status}
          </Text>
        </View>
        <CaretRight size={20} color="#6B7280" weight="bold" />
      </View>
    </TouchableOpacity>
  );

  const renderPaymentMethod = (
    method: string,
    icon: React.ReactNode,
    label: string,
  ) => (
    <TouchableOpacity
      style={[
        styles.paymentMethod,
        selectedPaymentMethod === method && styles.selectedPaymentMethod,
      ]}
      onPress={() => handlePaymentMethodSelect(method)}>
      {icon}
      <Text
        style={[
          styles.paymentMethodText,
          selectedPaymentMethod === method && styles.selectedPaymentMethodText,
        ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Layout title="Repay Money" showBack style={styles.layoutContainer}>
      <View style={styles.content}>
        {/* Search Input */}
        <View style={styles.searchContainer}>
          <MagnifyingGlass size={20} color="#9CA3AF" weight="bold" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by lender name"
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Outstanding Amount */}
        <View style={styles.outstandingContainer}>
          <Text style={styles.outstandingLabel}>Total Outstanding</Text>
          <Text style={styles.outstandingAmount}>
            ₹{totalOutstanding.toLocaleString()}
          </Text>
        </View>

        {/* Payment Methods */}
        <View style={styles.paymentMethodsContainer}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {renderPaymentMethod(
              'upi',
              <Wallet size={24} color={selectedPaymentMethod === 'upi' ? '#FFFFFF' : '#4F46E5'} weight="bold" />,
              'UPI',
            )}
            {renderPaymentMethod(
              'bank',
              <Bank size={24} color={selectedPaymentMethod === 'bank' ? '#FFFFFF' : '#4F46E5'} weight="bold" />,
              'Bank Transfer',
            )}
            {renderPaymentMethod(
              'card',
              <CreditCard size={24} color={selectedPaymentMethod === 'card' ? '#FFFFFF' : '#4F46E5'} weight="bold" />,
              'Card',
            )}
          </ScrollView>
        </View>

        {/* Loans List */}
        <View style={styles.loansContainer}>
          <Text style={styles.sectionTitle}>Active Loans</Text>
          <FlatList
            data={loans.filter(loan =>
              loan.lender.toLowerCase().includes(searchQuery.toLowerCase()),
            )}
            renderItem={renderLoanItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </View>
    </Layout>
  );
};

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    marginLeft: 8,
    fontSize: 16,
    color: '#1F2937',
  },
  outstandingContainer: {
    backgroundColor: '#4F46E5',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
  },
  outstandingLabel: {
    color: '#FFFFFF',
    opacity: 0.9,
    fontSize: 14,
    marginBottom: 4,
    fontFamily: 'Montserrat-Medium',
  },
  outstandingAmount: {
    color: '#FFFFFF',
    fontSize: 32,
    fontFamily: 'Montserrat-Bold',
  },
  paymentMethodsContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Montserrat-SemiBold',
    color: '#1F2937',
    marginBottom: 12,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 12,
  },
  selectedPaymentMethod: {
    backgroundColor: '#4F46E5',
  },
  paymentMethodText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Montserrat-Medium',
    color: '#4F46E5',
  },
  selectedPaymentMethodText: {
    color: '#FFFFFF',
  },
  loansContainer: {
    flex: 1,
  },
  loanItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  loanItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lenderIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#EEF2FF',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  loanDetails: {
    justifyContent: 'center',
  },
  lenderName: {
    fontSize: 16,
    fontFamily: 'Montserrat-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  dueDate: {
    fontSize: 14,
    fontFamily: 'Montserrat-Regular',
    color: '#6B7280',
  },
  loanItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amount: {
    fontSize: 16,
    fontFamily: 'Montserrat-SemiBold',
    color: '#1F2937',
    marginRight: 8,
  },
  statusBadge: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  overdueBadge: {
    backgroundColor: '#FEE2E2',
  },
  paidBadge: {
    backgroundColor: '#DCFCE7',
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Montserrat-Medium',
    color: '#4F46E5',
  },
  overdueText: {
    color: '#DC2626',
  },
  paidText: {
    color: '#059669',
  },
});

export default RepayMoneyScreen;
