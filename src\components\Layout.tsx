import React, {ReactNode} from 'react';
import {View, StyleSheet, SafeAreaView} from 'react-native';
import Header from '../header/Header';

type LayoutProps = {
  children: ReactNode;
  title: string;
  showBack?: boolean;
  showNotification?: boolean;
  showProfile?: boolean;
  showMenu?: boolean;
  transparent?: boolean;
  onNotificationPress?: () => void;
  onProfilePress?: () => void;
  onMenuPress?: () => void;
  style?: object;
  rightContent?: ReactNode;
};

export default function Layout({
  children,
  title,
  showBack,
  showNotification,
  showProfile,
  showMenu,
  transparent,
  onNotificationPress,
  onProfilePress,
  onMenuPress,
  style,
  rightContent,
}: LayoutProps) {
  return (
    <SafeAreaView style={styles.safeArea}>
      <Header
        title={title}
        showBack={showBack}
        showNotification={showNotification}
        showProfile={showProfile}
        showMenu={showMenu}
        transparent={transparent}
        onNotificationPress={onNotificationPress}
        onProfilePress={onProfilePress}
        onMenuPress={onMenuPress}
        rightContent={rightContent}
      />
      <View style={[styles.container, style]}>{children}</View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
}); 