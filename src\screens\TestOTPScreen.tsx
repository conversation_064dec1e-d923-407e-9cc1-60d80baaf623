import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import {authService} from '../services/authService';
import {
  testOTPConfiguration,
  checkAmplifyConfig,
  diagnoseSMSIssues,
} from '../utils/testOTP';

export default function TestOTPScreen() {
  const [phoneNumber, setPhoneNumber] = useState('+91'); // Default with country code
  const [otpCode, setOtpCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[OTP Test] ${message}`);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const testAmplifyConfig = () => {
    addLog('Testing Amplify Configuration...');
    const config = checkAmplifyConfig();
    if (config) {
      addLog('Amplify config loaded successfully');
      addLog(`Region: ${config.Auth?.Cognito?.region || 'Not set'}`);
      addLog(`User Pool ID: ${config.Auth?.Cognito?.userPoolId || 'Not set'}`);
    } else {
      addLog('Failed to load Amplify config');
    }
  };

  const runSMSDiagnosis = async () => {
    addLog('Running SMS Diagnosis...');
    const diagnosis = await diagnoseSMSIssues(phoneNumber);

    if (diagnosis.issue) {
      addLog(`❌ Issue found: ${diagnosis.issue}`);
      addLog(`💡 Recommendation: ${diagnosis.recommendation}`);
    } else {
      addLog('✅ Phone number format is correct');
      addLog('📋 Check these common issues:');
      diagnosis.issues?.forEach((issue: any, index: number) => {
        addLog(`${index + 1}. ${issue.issue}: ${issue.description}`);
      });
    }
  };

  const handleTestOTP = async () => {
    if (!phoneNumber || phoneNumber.length < 10) {
      Alert.alert(
        'Error',
        'Please enter a valid phone number with country code (e.g., +911234567890)',
      );
      return;
    }

    try {
      setLoading(true);
      addLog(`Testing OTP send to: ${phoneNumber}`);

      const result = await testOTPConfiguration(phoneNumber);

      if (result.success) {
        addLog('✅ OTP sent successfully!');
        addLog(`Response: ${JSON.stringify(result.result, null, 2)}`);
        Alert.alert('Success', 'OTP sent successfully! Check your phone.');
      } else {
        addLog('❌ OTP send failed');
        addLog(`Error: ${result.error.message}`);
        addLog(`Error Name: ${result.error.name}`);
        Alert.alert('Error', result.error.message);
      }
    } catch (error: any) {
      addLog(`❌ Unexpected error: ${error.message}`);
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (!phoneNumber || !otpCode) {
      Alert.alert('Error', 'Please enter both phone number and OTP code');
      return;
    }

    try {
      setLoading(true);
      addLog(
        `Testing OTP verification for: ${phoneNumber} with code: ${otpCode}`,
      );

      const result = await authService.verifyOTP(phoneNumber, otpCode);
      addLog('✅ OTP verified successfully!');
      addLog(`Response: ${JSON.stringify(result, null, 2)}`);
      Alert.alert('Success', 'OTP verified successfully!');
    } catch (error: any) {
      addLog(`❌ OTP verification failed: ${error.message}`);
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>OTP Testing Screen</Text>

      <TouchableOpacity style={styles.button} onPress={testAmplifyConfig}>
        <Text style={styles.buttonText}>Test Amplify Config</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={runSMSDiagnosis}>
        <Text style={styles.buttonText}>🔍 Diagnose SMS Issues</Text>
      </TouchableOpacity>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Phone Number (with country code):</Text>
        <TextInput
          style={styles.input}
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          placeholder="+911234567890"
          keyboardType="phone-pad"
        />
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleTestOTP}
        disabled={loading}>
        <Text style={styles.buttonText}>
          {loading ? 'Sending...' : 'Send Test OTP'}
        </Text>
      </TouchableOpacity>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>OTP Code:</Text>
        <TextInput
          style={styles.input}
          value={otpCode}
          onChangeText={setOtpCode}
          placeholder="123456"
          keyboardType="number-pad"
          maxLength={6}
        />
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleVerifyOTP}
        disabled={loading}>
        <Text style={styles.buttonText}>
          {loading ? 'Verifying...' : 'Verify OTP'}
        </Text>
      </TouchableOpacity>

      <View style={styles.logsContainer}>
        <View style={styles.logsHeader}>
          <Text style={styles.logsTitle}>Debug Logs</Text>
          <TouchableOpacity onPress={clearLogs}>
            <Text style={styles.clearButton}>Clear</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.logsScroll}>
          {logs.map((log, index) => (
            <Text key={index} style={styles.logText}>
              {log}
            </Text>
          ))}
        </ScrollView>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  button: {
    backgroundColor: '#003366',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  logsContainer: {
    marginTop: 20,
    flex: 1,
  },
  logsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  logsTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  clearButton: {
    color: '#007AFF',
    fontSize: 16,
  },
  logsScroll: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 10,
    maxHeight: 300,
  },
  logText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
});
