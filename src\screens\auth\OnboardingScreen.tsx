// src/screens/auth/OnboardingScreen.tsx
import React from 'react';
import {View, Text, StyleSheet, StatusBar, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {LinearGradient} from 'react-native-linear-gradient';

const OnboardingScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar translucent backgroundColor="transparent" />
      <LinearGradient
        colors={['#EDF7F4', '#E5EFFB']}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        style={styles.gradient}>
        <View style={styles.content}>
          <Image
            source={require('../../assets/images/welcome.png')}
            // style={tw`flex-1 justify-center items-center bg-blue-500 w-full h-[20px]`}
            style={styles.img}></Image>
          <Text style={styles.title}>Welcome to PAYZLE test</Text>
          <Text style={styles.subtitle}>
            This is where the onboarding process will begin
          </Text>
          {/* Add onboarding content here */}
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontFamily: 'Montserrat-SemiBold',
    fontSize: 24,
    color: '#003272',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: 'Montserrat-Medium',
    fontSize: 16,
    color: '#003272',
    textAlign: 'center',
    opacity: 0.8,
  },
  img: {
    height: 100,
  },
});

export default OnboardingScreen;
