// screens/home.js
import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import Layout from '../../components/Layout';
import {CaretRight} from 'phosphor-react-native';
import RepaymentCarousel from './carousel';
import ActiveCard from '../../globalComponents/activeCard';

interface ActiveCase {
  name: string;
  amountTitle: string;
  amount: string;
  date: string;
  tag: string;
  image: string;
}

const data: ActiveCase[] = [
  {
    name: '<PERSON><PERSON><PERSON> <PERSON>',
    amountTitle: 'Amount to be repaid',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: '<PERSON>oh<PERSON> <PERSON>',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: 'Rohit Sharma',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
  {
    name: 'Saksham Jha',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: 'Rohit Sharma',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
];

export default function HomeScreen() {
  const navigation = useNavigation();

  const handleNotificationPress = () => {
    navigation.navigate('Notifications' as never);
  };

  const handleProfilePress = () => {
    navigation.navigate('Account' as never);
  };

  const handleBorrowPress = () => {
    navigation.navigate('BorrowMoney' as never);
  };

  const handleRepayPress = () => {
    navigation.navigate('RepayMoney' as never);
  };

  return (
    <Layout
      title="Home"
      showNotification
      showProfile
      onNotificationPress={handleNotificationPress}
      onProfilePress={handleProfilePress}
      style={styles.layoutContainer}>
      <ScrollView className="flex-1">
        {/* Test Tailwind - Remove after confirming it works */}
        <View className="bg-green-500 p-4 m-4 rounded-xl border-2 border-green-700">
          <Text className="text-white text-lg font-bold text-center">
            ✅ Tailwind CSS is Working!
          </Text>
          <Text className="text-green-100 text-sm text-center mt-2">
            If you see this green box with white text, Tailwind is configured
            correctly!
          </Text>
        </View>

        {/* Repayment Carousel */}
        <View className="my-4">
          <RepaymentCarousel />
        </View>

        {/* Quick Actions */}
        <View className="bg-white rounded-2xl mx-4 flex-row gap-4 shadow-sm">
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleBorrowPress}>
            <View className="flex-row items-center bg-blue-50 p-3 rounded-lg">
              <Image
                source={require('../../assets/images/borrow.png')}
                className="w-6 h-6 mr-3"
                resizeMode="contain"
              />
              <Text className="text-blue-700 text-base font-medium">
                Borrow Money
              </Text>
            </View>
            <CaretRight size={20} color="#6B7280" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleRepayPress}>
            <View className="flex-row items-center bg-green-50 p-3 rounded-lg">
              <Image
                source={require('../../assets/images/repay.png')}
                className="w-6 h-6 mr-3"
                resizeMode="contain"
              />
              <Text className="text-green-700 text-base font-medium">
                Repay Money
              </Text>
            </View>
            <CaretRight size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Active Cases */}
        <View className="p-4">
          <Text className="text-lg font-semibold text-gray-800 mb-4">
            Active Cases
          </Text>
          {data.map((item, index) => (
            <View key={index} className="mb-3">
              <ActiveCard
                name={item.name}
                amountTitle={item.amountTitle}
                amount={item.amount}
                dueDate={item.date}
                profileImage={item.image}
              />
            </View>
          ))}
        </View>
      </ScrollView>
    </Layout>
  );
}

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#F9FAFB',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderWidth: 1,
    borderBottomColor: '#E5E7EB',
    borderRadius: 12,
    width: '48%',
    paddingHorizontal: 16,
  },
});
