import React from 'react';
import {Text, StyleSheet, ScrollView, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LoanCard from '../../../globalComponents/LoanCard';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../../../App';
import TitleHeader from '../../../header/titleHeader';
import theme from '../../../theme';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const LendDataArray = [
  {
    Date: '2nd Jan, 2025',
    lendData: [
      {
        name: '<PERSON><PERSON><PERSON>',
        tag: 'Real Friends',
        amount: '₹20,000',
        dueDate: '2nd Jan, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=3',
        onRepay: () => console.log('Repay pressed for <PERSON><PERSON><PERSON>'),
        gradientColors: ['#E0F7FA', '#B2EBF2'],
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        tag: 'College Buddies',
        amount: '₹15,500',
        dueDate: '12th Feb, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=5',
        onRepay: () => console.log('Repay pressed for Virat Kohli'),
        gradientColors: ['#FCE4EC', '#F8BBD0'],
      },
      {
        name: 'KL Rahul',
        tag: 'Close Circle',
        amount: '₹10,000',
        dueDate: '5th Mar, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=7',
        onRepay: () => console.log('Repay pressed for KL Rahul'),
        gradientColors: ['#FFF3E0', '#FFE0B2'],
      },
      {
        name: 'Hardik Pandya',
        tag: 'Gym Bro',
        amount: '₹8,200',
        dueDate: '18th Apr, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=9',
        onRepay: () => console.log('Repay pressed for Hardik Pandya'),
        gradientColors: ['#E8F5E9', '#C8E6C9'],
      },
      {
        name: 'MS Dhoni',
        tag: 'Mentor',
        amount: '₹25,000',
        dueDate: '30th May, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=11',
        onRepay: () => console.log('Repay pressed for MS Dhoni'),
        gradientColors: ['#EDE7F6', '#D1C4E9'],
      },
    ],
  },
  {
    Date: '2nd Jan, 2025',
    lendData: [
      {
        name: 'Rohit Sharma',
        tag: 'Real Friends',
        amount: '₹20,000',
        dueDate: '2nd Jan, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=3',
        onRepay: () => console.log('Repay pressed for Rohit Sharma'),
        gradientColors: ['#E0F7FA', '#B2EBF2'],
      },
      {
        name: 'Virat Kohli',
        tag: 'College Buddies',
        amount: '₹15,500',
        dueDate: '12th Feb, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=5',
        onRepay: () => console.log('Repay pressed for Virat Kohli'),
        gradientColors: ['#FCE4EC', '#F8BBD0'],
      },
      {
        name: 'KL Rahul',
        tag: 'Close Circle',
        amount: '₹10,000',
        dueDate: '5th Mar, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=7',
        onRepay: () => console.log('Repay pressed for KL Rahul'),
        gradientColors: ['#FFF3E0', '#FFE0B2'],
      },
      {
        name: 'Hardik Pandya',
        tag: 'Gym Bro',
        amount: '₹8,200',
        dueDate: '18th Apr, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=9',
        onRepay: () => console.log('Repay pressed for Hardik Pandya'),
        gradientColors: ['#E8F5E9', '#C8E6C9'],
      },
      {
        name: 'MS Dhoni',
        tag: 'Mentor',
        amount: '₹25,000',
        dueDate: '30th May, 2025',
        profileImage: 'https://i.pravatar.cc/150?img=11',
        onRepay: () => console.log('Repay pressed for MS Dhoni'),
        gradientColors: ['#EDE7F6', '#D1C4E9'],
      },
    ],
  },
];
const LendHistoryScreen = () => {
  const navigation = useNavigation<NavigationProp>();

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {LendDataArray.map((item, index) => (
          <View style={styles.container}>
            <Text style={styles.date}>{item.Date}</Text>
            {item.lendData.map((friend, index) => (
              <LoanCard
                key={index}
                name={friend.name}
                tag={friend.tag}
                amount={friend.amount}
                dueDate={friend.dueDate}
                profileImage={friend.profileImage}
                onRepay={() =>
                  navigation.navigate('LoanHistoryDetails', {friend})
                }
                gradientColors={friend.gradientColors}
              />
            ))}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // marginBottom: 6,
    paddingHorizontal: 4,
  },
  scrollContent: {
    padding: 0,
  },
  date: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: 'Montserrat-SemiBold',
  },
});

export default LendHistoryScreen;
