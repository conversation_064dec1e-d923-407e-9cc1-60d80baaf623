const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resolver: {
    blockList: [
      // Ignore build directories that cause ENOENT errors
      /.*\/node_modules\/@aws-amplify\/react-native\/android\/build\/.*/,
      /.*\/node_modules\/@aws-amplify\/react-native\/ios\/build\/.*/,
      /.*\/android\/build\/.*/,
      /.*\/ios\/build\/.*/,
    ],
  },
  watchFolders: [
    // Only watch the root directory and avoid problematic subdirectories
    path.resolve(__dirname),
  ],
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
