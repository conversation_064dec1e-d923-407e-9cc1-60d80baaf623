// src/screens/auth/OnboardingScreen.tsx
import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Layout from '../../components/Layout';
import {CurrencyDollar, User, Calendar} from 'phosphor-react-native';

const BorrowMoneyScreen = () => {
  const [amount, setAmount] = useState('');
  const [duration, setDuration] = useState('');
  const [purpose, setPurpose] = useState('');

  return (
    <Layout
      title="Borrow Money"
      showBack
      transparent
      style={styles.layoutContainer}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Amount Input Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>How much do you need?</Text>
            <View style={styles.inputContainer}>
              <CurrencyDollar size={24} color="#4F46E5" weight="bold" />
              <TextInput
                style={styles.input}
                placeholder="Enter amount"
                keyboardType="numeric"
                value={amount}
                onChangeText={setAmount}
                placeholderTextColor="#9CA3AF"
              />
            </View>
          </View>

          {/* Duration Input Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>For how long?</Text>
            <View style={styles.inputContainer}>
              <Calendar size={24} color="#4F46E5" weight="bold" />
              <TextInput
                style={styles.input}
                placeholder="Duration in days"
                keyboardType="numeric"
                value={duration}
                onChangeText={setDuration}
                placeholderTextColor="#9CA3AF"
              />
            </View>
          </View>

          {/* Purpose Input Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Purpose of borrowing</Text>
            <View style={styles.inputContainer}>
              <User size={24} color="#4F46E5" weight="bold" />
              <TextInput
                style={styles.input}
                placeholder="Enter purpose"
                value={purpose}
                onChangeText={setPurpose}
                placeholderTextColor="#9CA3AF"
                multiline
              />
            </View>
          </View>

          {/* Terms and Conditions */}
          <View style={styles.termsContainer}>
            <Text style={styles.termsText}>
              By proceeding, you agree to our terms and conditions for borrowing
              money.
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Button */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity style={styles.submitButton}>
          <Text style={styles.submitButtonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    </Layout>
  );
};

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  input: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  termsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
  },
  termsText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  bottomContainer: {
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  submitButton: {
    backgroundColor: '#4F46E5',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BorrowMoneyScreen;
