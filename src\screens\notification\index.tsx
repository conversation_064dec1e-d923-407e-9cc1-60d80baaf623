import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import Layout from '../../components/Layout';

interface NotificationMessage {
  text: string;
  time: string;
}

interface NotificationGroup {
  date: string;
  backgroundColor?: string;
  messages: NotificationMessage[];
}

const notifications: NotificationGroup[] = [
  {
    date: 'Today',
    backgroundColor: '#EEF2FF',
    messages: [
      {
        text: '<PERSON><PERSON><PERSON> added you as a friend.',
        time: '11:30 AM',
      },
      {
        text: '<PERSON><PERSON><PERSON> added you as a friend.',
        time: '11:30 AM',
      },
      {
        text: '<PERSON><PERSON><PERSON> added you as a friend.',
        time: '11:30 AM',
      },
    ],
  },
  {
    date: 'Jan 10, 2025',
    backgroundColor: '#FFFFFF',
    messages: [
      {
        text: 'You have to repay <PERSON><PERSON><PERSON> in 4 days.',
        time: '11:30 AM',
      },
      {
        text: 'You have to repay <PERSON><PERSON><PERSON> in 4 days.',
        time: '11:30 AM',
      },
      {
        text: 'You have to repay <PERSON><PERSON><PERSON> in 4 days.',
        time: '11:30 AM',
      },
      {
        text: 'You have to repay <PERSON><PERSON><PERSON> in 4 days.',
        time: '11:30 AM',
      },
      {
        text: 'You have to repay <PERSON><PERSON><PERSON> in 4 days.',
        time: '11:30 AM',
      },
    ],
  },
  {
    date: 'Jan 09, 2025',
    messages: [
      {
        text: 'You have to repay Rohan in 4 days.',
        time: '11:30 AM',
      },
    ],
  },
];

const NotificationsScreen: React.FC = () => {
  return (
    <Layout title="Notifications" showBack style={styles.layoutContainer}>
      <ScrollView style={styles.scrollView}>
        {notifications.map((group, groupIndex) => (
          <View key={groupIndex} style={styles.section}>
            <Text style={styles.dateHeader}>{group.date}</Text>
            <View style={styles.notificationsGroup}>
              {group.messages.map((notification, notificationIndex) => (
                <View
                  key={`${groupIndex}-${notificationIndex}`}
                  style={[
                    styles.notificationCard,
                    {
                      backgroundColor:
                        group.backgroundColor || styles.notificationCard.backgroundColor,
                    },
                  ]}>
                  <Text style={styles.notificationText}>
                    {notification.text}
                  </Text>
                  <Text style={styles.timeText}>{notification.time}</Text>
                </View>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  dateHeader: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  notificationsGroup: {
    backgroundColor: '#FFFFFF',
  },
  notificationCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  notificationText: {
    flex: 1,
    fontSize: 14,
    color: '#1F2937',
    lineHeight: 20,
  },
  timeText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 12,
  },
});

export default NotificationsScreen;
