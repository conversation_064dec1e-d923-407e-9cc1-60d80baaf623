// src/theme/index.ts
export const colors = {
    // Primary colors
    primary: '#003272',
    secondary: '#1E88E5',
    accent: '#6FCF97',
    
    // Background colors
    background: {
      gradient: {
        start: '#EDF7F4',
        end: '#E5EFFB',
      },
      light: '#FFFFFF',
      dark: '#F5F5F5',
    },
    
    // Text colors
    text: {
      primary: '#003272',
      secondary: '#555555',
      disabled: '#9E9E9E',
      light: '#FFFFFF',
    },
    
    // Status colors
    status: {
      success: '#27AE60',
      warning: '#F2C94C',
      error: '#EB5757',
      info: '#2F80ED',
    },
    
    // Border colors
    border: {
      light: '#E0E0E0',
      medium: '#BDBDBD',
      dark: '#9E9E9E',
    },
  };
  
  export const typography = {
    fontFamily: {
      regular: 'Montserrat-Regular',
      medium: 'Montserrat-Medium',
      semiBold: 'Montserrat-SemiBold',
      bold: 'Montserrat-Bold',
    },
    fontSize: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36,
      '5xl': 48,
    },
    lineHeight: {
      xs: 16,
      sm: 20,
      md: 24,
      lg: 28,
      xl: 32,
      '2xl': 36,
      '3xl': 40,
      '4xl': 48,
      '5xl': 60,
    },
  };
  
  export const spacing = {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  };
  
  export const shadows = {
    sm: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.18,
      shadowRadius: 1.0,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.30,
      shadowRadius: 4.65,
      elevation: 8,
    },
  };
  
  export const borderRadius = {
    sm: 4,
    md: 8,
    lg: 16,
    xl: 24,
    '2xl': 32,
    full: 9999,
  };
  
  // Export all theme variables
  export default {
    colors,
    typography,
    spacing,
    shadows,
    borderRadius,
  };