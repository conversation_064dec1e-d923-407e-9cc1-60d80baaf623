// src/screens/auth/OnboardingScreen.tsx
import React from 'react';
import {View, Text, StyleSheet, StatusBar, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {LinearGradient} from 'react-native-linear-gradient';
import tw from 'tailwind-react-native-classnames';

const AccountTagsScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <Text>AccountTagsScreen</Text>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontFamily: 'Montserrat-SemiBold',
    fontSize: 24,
    color: '#003272',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: 'Montserrat-Medium',
    fontSize: 16,
    color: '#003272',
    textAlign: 'center',
    opacity: 0.8,
  },
  img: {
    height: 100,
  },
});

export default AccountTagsScreen;
