import React, { useState } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Alert,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';

export const AuthScreen: React.FC = () => {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [otpCode, setOtpCode] = useState('');
    const [isOtpSent, setIsOtpSent] = useState(false);
    const { signUpWithPhone, requestOTP, verifyOTP } = useAuth();

    const handleSendOTP = async () => {
        try {
            // First try to sign up the user
            await signUpWithPhone(phoneNumber);
            // Then request OTP
            await requestOTP(phoneNumber);
            setIsOtpSent(true);
            Alert.alert('Success', 'OTP has been sent to your phone number');
        } catch (error: any) {
            Alert.alert('Error', error.message);
        }
    };

    const handleVerifyOTP = async () => {
        try {
            await verifyOTP(phoneNumber, otpCode);
            Alert.alert('Success', 'Phone number verified successfully');
        } catch (error: any) {
            Alert.alert('Error', error.message);
        }
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Phone Authentication</Text>
            
            <TextInput
                style={styles.input}
                placeholder="Enter phone number (+1234567890)"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="phone-pad"
                editable={!isOtpSent}
            />

            {!isOtpSent ? (
                <TouchableOpacity style={styles.button} onPress={handleSendOTP}>
                    <Text style={styles.buttonText}>Send OTP</Text>
                </TouchableOpacity>
            ) : (
                <>
                    <TextInput
                        style={styles.input}
                        placeholder="Enter OTP"
                        value={otpCode}
                        onChangeText={setOtpCode}
                        keyboardType="number-pad"
                    />
                    <TouchableOpacity style={styles.button} onPress={handleVerifyOTP}>
                        <Text style={styles.buttonText}>Verify OTP</Text>
                    </TouchableOpacity>
                </>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        padding: 20,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 20,
        textAlign: 'center',
    },
    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        padding: 15,
        borderRadius: 8,
        marginBottom: 15,
    },
    button: {
        backgroundColor: '#007AFF',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },
}); 