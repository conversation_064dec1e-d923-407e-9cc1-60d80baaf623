// Test utility to debug OTP issues
import {authService} from '../services/authService';

export const testOTPConfiguration = async (phoneNumber: string) => {
  console.log('=== OTP Configuration Test ===');
  console.log('Phone number:', phoneNumber);

  try {
    console.log('Testing OTP send...');
    const result = await authService.sendOTP(phoneNumber);
    console.log('OTP send successful:', result);
    return {success: true, result};
  } catch (error: any) {
    console.error('OTP send failed:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      code: error.code,
    });
    return {success: false, error};
  }
};

// Function to check AWS Amplify configuration
export const checkAmplifyConfig = () => {
  console.log('=== Amplify Configuration Check ===');

  try {
    const {Amplify} = require('aws-amplify');
    const config = Amplify.getConfig();
    console.log('Current Amplify config:', JSON.stringify(config, null, 2));
    return config;
  } catch (error) {
    console.error('Failed to get Amplify config:', error);
    return null;
  }
};

// Function to diagnose SMS sandbox issues
export const diagnoseSMSIssues = async (phoneNumber: string) => {
  console.log('=== SMS Sandbox Diagnosis ===');
  console.log('Phone number:', phoneNumber);

  // Check if phone number is properly formatted
  if (!phoneNumber.startsWith('+')) {
    console.log('❌ Phone number should start with + (country code)');
    return {
      issue: 'Phone number format',
      recommendation: 'Add country code (+91 for India)',
    };
  }

  if (phoneNumber.length < 10) {
    console.log('❌ Phone number too short');
    return {
      issue: 'Phone number length',
      recommendation: 'Use full phone number with country code',
    };
  }

  console.log('✅ Phone number format looks correct');

  // Common SMS sandbox issues
  const commonIssues = [
    {
      issue: 'AWS SMS Sandbox',
      description: 'Your AWS account is in SMS sandbox mode',
      symptoms: [
        'Phone number gets stored in Cognito',
        'No SMS received',
        'No error in logs',
      ],
      solution:
        'Verify your phone number in AWS SNS console or request production access',
    },
    {
      issue: 'Missing IAM Role',
      description: 'Cognito lacks permission to send SMS via SNS',
      symptoms: ['Error in AWS CloudWatch logs', 'Permission denied errors'],
      solution: 'Create IAM role with SNS permissions for Cognito',
    },
    {
      issue: 'SMS Spending Limit',
      description: 'AWS SMS spending limit reached ($1 default)',
      symptoms: ['SMS worked before but stopped', 'Billing alerts'],
      solution: 'Increase SMS spending limit in AWS console',
    },
  ];

  console.log('Common SMS issues to check:');
  commonIssues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.issue}: ${issue.description}`);
    console.log(`   Solution: ${issue.solution}`);
  });

  return {issues: commonIssues};
};
