import React from 'react';
import {Text, StyleSheet, ScrollView, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import TitleHeader from '../../../header/titleHeader';
import LoanCard from '../../../globalComponents/LoanCard';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

type RootStackParamList = {
  LoanHistoryDetails: {friend: any};
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const LenderDataArray = [
  {
    Date: '24th Mar, 2024',
    lendData: [
      {
        name: '<PERSON>',
        tag: 'Work Colleague',
        amount: '₹35,000',
        dueDate: '24th Apr, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=1',
        onRepay: () => console.log('Repay pressed for <PERSON>'),
        gradientColors: ['#E0F7FA', '#B2EBF2'],
      },
      {
        name: '<PERSON>',
        tag: 'College Friend',
        amount: '₹12,500',
        dueDate: '30th Apr, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=2',
        onRepay: () => console.log('Repay pressed for Michael Chen'),
        gradientColors: ['#FCE4EC', '#F8BBD0'],
      }
    ],
  },
  {
    Date: '20th Mar, 2024',
    lendData: [
      {
        name: 'Priya Patel',
        tag: 'Family Friend',
        amount: '₹50,000',
        dueDate: '20th May, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=3',
        onRepay: () => console.log('Repay pressed for Priya Patel'),
        gradientColors: ['#FFF3E0', '#FFE0B2'],
      },
      {
        name: 'David Wilson',
        tag: 'Business Partner',
        amount: '₹75,000',
        dueDate: '20th Jun, 2024',
        profileImage: 'https://i.pravatar.cc/150?img=4',
        onRepay: () => console.log('Repay pressed for David Wilson'),
        gradientColors: ['#E8F5E9', '#C8E6C9'],
      }
    ],
  }
];

const LenderModeScreen = () => {
  const navigation = useNavigation<NavigationProp>();

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {LenderDataArray.map((item, index) => (
          <View style={styles.container}>
            <Text style={styles.date}>{item.Date}</Text>
            {item.lendData.map((friend, index) => (
              <LoanCard
                key={index}
                name={friend.name}
                tag={friend.tag}
                amount={friend.amount}
                dueDate={friend.dueDate}
                profileImage={friend.profileImage}
                onRepay={() =>
                  navigation.navigate('LoanHistoryDetails', {friend})
                }
                gradientColors={friend.gradientColors}
              />
            ))}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: 8,
    paddingHorizontal: 4,
  },
  scrollContent: {
    // paddingVertical: 16,
    // paddingHorizontal: 16,
  },
  date: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: 'Montserrat-SemiBold',
  },
});

export default LenderModeScreen;
