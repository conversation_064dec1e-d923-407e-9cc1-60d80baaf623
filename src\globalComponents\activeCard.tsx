import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

type LoanCardProps = {
  name?: string;
  tag?: string;
  amount?: string;
  dueDate?: string;
  onRepay?: () => void;
  profileImage?: string;
  containerStyle?: StyleProp<ViewStyle>;
  gradientColors?: string[]; // <-- Add this line
  amountTitle?: string;
};

export default function ActiveCard({
  name,
  tag,
  amount,
  dueDate,
  onRepay,
  profileImage,
  containerStyle,
  gradientColors,
  amountTitle,
}: LoanCardProps) {
  return (
    <View style={[styles.card, containerStyle]}>
      {/* Top Section */}
      <View style={styles.topRow}>
        <Image source={{uri: profileImage}} style={styles.avatar} />
        <View style={{marginLeft: 12}}>
          <Text style={styles.name}>{name}</Text>
          <Text style={styles.amountouter}>
            {' '}
            {amountTitle} - Rs.<Text style={styles.amount}> {amount}</Text>
          </Text>
        </View>
      </View>

      {/* Bottom Section */}
      <View style={styles.bottomRow}>
        <View>
          <Text style={styles.repayLabel}>Repayment date</Text>
          <Text style={styles.repayDate}>{dueDate}</Text>
        </View>
        <TouchableOpacity onPress={onRepay} style={styles.repayButton}>
          <Text style={styles.repayButtonText}>Repay</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#D4D4D4',
    width: '100%',
    marginHorizontal: 0,
    marginTop: 12,
  },
  topRow: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: '#D4D4D4',
  },
  avatar: {
    width: 52,
    height: 52,
    borderRadius: 26,
  },
  name: {
    fontSize: 16,
    fontFamily: 'Montserrat-SemiBold',
    color: '#000000',
  },
  badge: {
    marginTop: 4,
    backgroundColor: '#E3F4EC',
    paddingVertical: 2,
    paddingHorizontal: 10,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  badgeText: {
    fontSize: 12,
    fontFamily: 'Montserrat-Medium',
    color: '#176B47',
  },
  amountSection: {
    marginLeft: 'auto',
    alignItems: 'flex-end',
  },
  label: {
    fontSize: 12,
    fontFamily: 'Montserrat-Regular',
    color: '#8A8A8A',
  },
  amountouter: {
    fontSize: 16,
    // fontFamily: 'Montserrat-Bold',
    color: '#5D5D5D',
    marginTop: 2,
  },
  amount: {
    fontSize: 16,
    fontFamily: 'Montserrat-Bold',
    color: '#0057E7',
    marginTop: 2,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  repayLabel: {
    fontSize: 12,
    fontFamily: 'Montserrat-Regular',
    color: '#4B4B4B',
  },
  repayDate: {
    fontSize: 14,
    fontFamily: 'Montserrat-SemiBold',
    color: '#D02B20',
    marginTop: 2,
  },
  repayButton: {
    backgroundColor: '#00378A',
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderRadius: 22,
  },
  repayButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: 'Montserrat-SemiBold',
  },
});
