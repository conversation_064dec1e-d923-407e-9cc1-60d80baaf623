import React from 'react';
import Layout from '../../../components/Layout';
// import ActiveCasesComponent from './components/ActiveCasesComponent';
import { StyleSheet, Text } from 'react-native';

const ActiveCasesScreen = () => {
  return (
    <Layout title="Active Cases" showBack style={styles.container}>
      {/* <ActiveCasesComponent /> */}
      <Text>Tags Component</Text>

    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
});

export default ActiveCasesScreen; 