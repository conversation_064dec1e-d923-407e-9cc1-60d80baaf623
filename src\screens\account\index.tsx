import React from 'react';
import {View, Text, Image, TouchableOpacity, ScrollView, StyleSheet} from 'react-native';
import Layout from '../../components/Layout';
import {
  User,
  Clock,
  Users,
  Briefcase,
  Tag,
  Gear,
  SignOut,
  CaretRight,
} from 'phosphor-react-native';

interface MenuItem {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  onPress?: () => void;
}

export default function AccountScreen({navigation}: {navigation: any}) {
  const handleLogout = () => {
    // Implement logout logic
    console.log('Logout pressed');
  };

  const menuItems: MenuItem[] = [
    {
      icon: <User size={24} color="#4F46E5" weight="duotone" />,
      title: 'Profile',
      subtitle: 'Name, Phone number, Email & Birthday',
      onPress: () => navigation.navigate('Profile'),
    },
    {
      icon: <Clock size={24} color="#4F46E5" weight="duotone" />,
      title: 'History',
      subtitle: 'Loan history & Lend history',
      onPress: () => navigation.navigate('History'),
    },
    {
      icon: <Users size={24} color="#4F46E5" weight="duotone" />,
      title: 'Friends',
      subtitle: 'List of all your friends',
      onPress: () => navigation.navigate('Friends'),
    },
    {
      icon: <Briefcase size={24} color="#4F46E5" weight="duotone" />,
      title: 'Active Cases',
      subtitle: 'List of all your active loans & lends',
      onPress: () => navigation.navigate('ActiveCases'),
    },
    {
      icon: <Tag size={24} color="#4F46E5" weight="duotone" />,
      title: 'Tags',
      subtitle: 'Create & view your tags',
      onPress: () => navigation.navigate('Tags'),
    },
    {
      icon: <Gear size={24} color="#4F46E5" weight="duotone" />,
      title: 'Settings',
      subtitle: 'Notifications & preferences',
      onPress: () => navigation.navigate('Settings'),
    },
  ];

  return (
    <Layout
      title="Account"
      showNotification
      style={styles.container}>
      {/* Profile Section */}
      <View style={styles.profileSection}>
        <View style={styles.profileImageContainer}>
          <Image
            source={{uri: 'https://i.pravatar.cc/200'}}
            style={styles.profileImage}
          />
          <View style={styles.profileBadge}>
            <Text style={styles.profileBadgeText}>Pro</Text>
          </View>
        </View>
        <View style={styles.profileInfo}>
          <Text style={styles.welcomeText}>Hey!</Text>
          <Text style={styles.nameText}>Rohit Singh</Text>
          <Text style={styles.phoneText}>+91 **********</Text>
        </View>
      </View>

      {/* Menu Items */}
      <ScrollView style={styles.menuContainer}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.menuItem,
              index === menuItems.length - 1 && styles.lastMenuItem,
            ]}
            onPress={item.onPress}>
            <View style={styles.menuItemIconContainer}>{item.icon}</View>
            <View style={styles.menuItemContent}>
              <Text style={styles.menuItemTitle}>{item.title}</Text>
              <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
            </View>
            <CaretRight size={20} color="#9CA3AF" weight="bold" />
          </TouchableOpacity>
        ))}

        {/* Stats Section */}
        <View style={styles.statsContainer}>
          <View style={styles.statsItem}>
            <Text style={styles.statsValue}>₹12,500</Text>
            <Text style={styles.statsLabel}>Total Lent</Text>
          </View>
          <View style={[styles.statsItem, styles.statsItemBorder]}>
            <Text style={styles.statsValue}>₹8,000</Text>
            <Text style={styles.statsLabel}>Total Borrowed</Text>
          </View>
          <View style={styles.statsItem}>
            <Text style={styles.statsValue}>15</Text>
            <Text style={styles.statsLabel}>Active Cases</Text>
          </View>
        </View>
      </ScrollView>

      {/* Logout Button */}
      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <SignOut size={20} color="#DC2626" weight="bold" />
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  profileImageContainer: {
    position: 'relative',
  },
  profileImage: {
    width: 72,
    height: 72,
    borderRadius: 36,
    borderWidth: 3,
    borderColor: '#4F46E5',
  },
  profileBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#4F46E5',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  profileBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  profileInfo: {
    marginLeft: 16,
  },
  welcomeText: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 4,
  },
  nameText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  phoneText: {
    fontSize: 14,
    color: '#6B7280',
  },
  menuContainer: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    marginTop: 24,
    marginHorizontal: 16,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statsItem: {
    flex: 1,
    alignItems: 'center',
  },
  statsItemBorder: {
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: '#E5E7EB',
    marginHorizontal: 16,
    paddingHorizontal: 16,
  },
  statsValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#4F46E5',
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    marginVertical: 16,
    paddingVertical: 12,
    backgroundColor: '#FEE2E2',
    borderRadius: 12,
  },
  logoutText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#DC2626',
  },
});
